import { Component, Input, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { faCoffee } from '@fortawesome/free-solid-svg-icons';
import { GsapScrollSmootherService } from '@/services/v3/utils/gsap-scroll-smoother.service';
@Component({
  selector: 'lib-blog-details',
  templateUrl: './blog-details.component.html'
})

export class BlogDetailsComponent implements OnInit {
  @Input() post;
  @Input() iframeUrl;
  faCoffee = faCoffee;
  public currentUrl = '';
  public contentSanitized: any;

  constructor (
    private sanitizer: DomSanitizer,
    private smoothScrollService: GsapScrollSmootherService
  ) {
    this.currentUrl = window.location.href;
  }

  ngOnInit () {
    this.contentSanitized = this.sanitizer.bypassSecurityTrustHtml(this.post.content);
  }

  onPrint () {
    window.print();
  }

  backTop(){
    this.smoothScrollService.scrollTo('#imgTop');
  }
}
