import { Component, OnInit } from '@angular/core';
import { BlogService } from '@/services/v3/blog/blog.service';
import { GsapScrollSmootherService } from '@/services/v3/utils/gsap-scroll-smoother.service';

@Component({
  selector: 'lib-blog-list-2',
  templateUrl: './blog-list-2.component.html'
})

export class BlogList2Component implements OnInit {
  public blogPosts = [];
  public blogCategories = [];

  public cPage: number = 1;
  public currentCategory: string;
  public totalLength: number;
  public limitPerPage = 9;

  constructor (
    private blog: BlogService,
    private smoothScrollService: GsapScrollSmootherService
  ) {}

  ngOnInit () {
    // Get posts
    this.blog.getPosts('', '').subscribe(({ data }) => {
      this.blogPosts = data;
      this.totalLength = data.length;
    });

    // Get blog categories
    this.blog.getCategories().subscribe(({ data }) => {
      this.blogCategories = data;
    });
  }

  onPageChange (number: number) {
    this.cPage = number;
    this.smoothScrollService.scrollTo('#blog-list');
  }

  onCategoryChange (categorySlug: string) {
    this.currentCategory = categorySlug;
    this.cPage = 1;
    this.totalLength = this.blogPosts.filter(p => p.category_slug === categorySlug).length;
  }
}
