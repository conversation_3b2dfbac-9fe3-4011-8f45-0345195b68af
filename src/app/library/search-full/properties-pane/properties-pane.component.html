<div id="properties-pane" class="search-properties-pane-cpn container-fluid" [class]="panelStatus" [class.show]="!showMapOnly">
	<a class="toggle-pane toggle-pane-1" [class.selected]="panelStatus === null" (click)="togglePane('left')">
		<div><span class="icon-arrow-right"></span></div>
	</a>
	<a class="toggle-pane toggle-pane-2" [class.selected]="panelStatus === 'grid'" (click)="togglePane('right')">
		<div><span class="icon-arrow-left"></span></div>
	</a>

	<div class="loading -center" *ngIf="loading">
		<div class="lds-ripple">
			<div></div>
			<div></div>
		</div>
	</div>

	<div class="properties-list-cpn container -wide" id="properties-list" *ngIf="!loading && properties?.length">
		<div class="properties-menu -flex-between" *ngIf="properties.length && !loading">
			<h1 class="-h4">{{ titleStr }}</h1>
			<ng-select
				class="transparent"
				[searchable]="false"
				[clearable]="false"
				[items]="sortableItems"
				bindLabel="name"
				bindValue="value"
				[(ngModel)]="order"
				[ngModelOptions]="{standalone: true}"
				placeholder="{{ 'library.search-full.properties-pane.order' | translate }}">
			</ng-select>
		</div>

		<div class="properties-list-ctn">
			<div class="properties" *ngFor="let property of properties | orderBy: order | paginate: { id: 'properties-pagination', itemsPerPage: 12, currentPage: currentPage}">
				<lib-properties [property]="property" [rentalSearch]="search.selectedInscriptionType === 2"></lib-properties>
			</div>
		</div>

		<pagination-controls id="properties-pagination" class="paginiation-controls"
			(pageChange)="onPageChange($event)"
			maxSize="5"
			directionLinks="true"
			previousLabel=""
			nextLabel=""
			autoHide="true">
		</pagination-controls>
	</div>
	<div class="no-results -center" *ngIf="!loading && properties.length==0">
		<p class="txt1">{{ 'library.search-full.properties-pane.no-result' | translate }}</p>
		<p class="txt2">{{ 'library.search-full.properties-pane.no-result-alert' | translate }}</p>
		<a [routerLink]="['urls.real-estate-alert' | translate ]" class="main-button -primary">
			<span class="icon-alerte"></span>
			{{ 'library.search-full.properties-pane.real-estate-alert' | translate }}
		</a>
	</div>
</div>
