import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';

import { ReCaptchaV3Service } from 'ng-recaptcha';
import { Subscription } from 'rxjs';

import { AlertService } from '@/services/v3/alert/alert.service';
import { BlocksService } from '@/services/v3/contentblocks/contentblocks.service';
import { GsapScrollSmootherService } from '@/services/v3/utils/gsap-scroll-smoother.service';

@Component({
  selector: 'lib-alert-form',
  templateUrl: './alert-form.component.html'
})

export class AlertFormComponent implements OnInit {
  public currentStep: number = 1;

  // All cities, sorted by neighborhood
  citiesList = {
    'Rive Sud': [
      { value: 'Ahuntsic' },
      { value: 'Centre-Est' },
      { value: 'Centre-ville' },
      { value: 'Côte-des-Neiges' },
      { value: 'Côte-St-Luc' },
      { value: 'Est de Montréal' },
      { value: 'Hampstead' },
      { value: 'Île des Soeurs' },
      { value: 'Lachine' },
      { value: 'Laurentides / Mont-Tremblant' },
      { value: 'Laval' },
      { value: 'Longueuil / Rive-Sud' }
    ],
    'Rive Nord': [
      { value: 'Montréal Centre-Ouest' },
      { value: 'Notre-Dame-de-Grâce' },
      { value: 'Outremont' },
      { value: 'Outremont Adj.' },
      { value: 'Plateau-Mont-Royal / Mile-End' },
      { value: 'Rive Nord' },
      { value: 'Rosemont / Petite-Patrie' },
      { value: "St-Adolphe-d'Howard" },
      { value: 'St-Lambert' },
      { value: 'Sud-Ouest' },
      { value: 'Verdun' },
      { value: 'Vieux-Montréal' },
      { value: 'Ville de Mont-Royal' },
      { value: 'Ville St-Laurent' },
      { value: 'Villeray / St-Michel / Parc Extension' },
      { value: 'Westmount' },
      { value: 'Westmount Adj.' }
    ]
  };

  // Helper for neighborhood names
  neighborhoodList = Object.keys(this.citiesList);

  priceRange = [
    { id: 1, name: '1+' },
    { id: 2, name: '2+' },
    { id: 3, name: '3+' },
    { id: 4, name: '4+' },
    { id: 5, name: '5+' }
  ];

  propertyTypes: any = ['NA'];
  cities: any = ['NA'];

  alertForm: UntypedFormGroup;
  firstName: UntypedFormControl;
  lastName: UntypedFormControl;
  phone: UntypedFormControl;
  phoneExt: UntypedFormControl;
  email: UntypedFormControl;
  other: UntypedFormControl;
  budget: UntypedFormControl;

  successMessage: boolean = false;
  errorMessage: boolean = false;

  isTyping = false;
  formSend: boolean = false;
  formLoading: boolean = false;

  public blockTitle: string;
  public blockContent: string;

  // Recaptcha v3 Subscription
  private submitExecutionSubscription: Subscription

  constructor (
    private recaptchaV3Service: ReCaptchaV3Service,
    private formBuilder: UntypedFormBuilder,
    private alertService: AlertService,
    private blocksService: BlocksService,
    private smoothScrollService: GsapScrollSmootherService
  ) {
    this.blocksService.getBlock('bloc-alerte-immobiliere').subscribe(data => {
      this.blockTitle = data.title;
      this.blockContent = data.text;
    });
  }

  ngOnInit () {
    this.createFormControls();
    this.createForm();
    this.delayRCDisplay();
  }

  delayRCDisplay () {
    setTimeout(() => {
      const result = document.getElementsByClassName('grecaptcha-badge');
      const recaptcha = result.item(0) as HTMLElement;
      recaptcha.style.visibility = 'visible';
    }, 2000);
  }

  ngOnDestroy () {
    const result = document.getElementsByClassName('grecaptcha-badge');
    const recaptcha = result.item(0) as HTMLElement;
    recaptcha.style.visibility = 'hidden';

    if (this.submitExecutionSubscription) {
      this.submitExecutionSubscription.unsubscribe();
    }
  }

  onPreviousStep ($event) {
    $event.preventDefault();
    this.currentStep--;
    setTimeout(() => this.smoothScrollService.scrollTo('#alertForm'), 100);
  }

  onNextStep ($event) {
    $event.preventDefault();
    this.currentStep++;
    setTimeout(() => this.smoothScrollService.scrollTo('#alertForm'), 100);
  }

  private createFormControls () {
    this.firstName = new UntypedFormControl('', Validators.required);
    this.lastName = new UntypedFormControl('', Validators.required);
    this.phone = new UntypedFormControl('', [
      Validators.required,
      Validators.pattern('[0-9]+'),
      Validators.minLength(10)
    ]);
    this.email = new UntypedFormControl('', [
      Validators.required,
      Validators.pattern('^\\w+([\\.-]?\\w+)*@\\w+([\\.-]?\\w+)*(\\.\\w{2,3})+$')
    ]);
    this.phoneExt = new UntypedFormControl('');
    this.other = new UntypedFormControl('');
    this.budget = new UntypedFormControl('');
  }

  private createForm () {
    this.alertForm = this.formBuilder.group({
      name: new UntypedFormGroup({
        firstName: this.firstName,
        lastName: this.lastName,
        phone: this.phone,
        phoneExt: this.phoneExt
      }),
      email: new UntypedFormGroup({
        email: this.email
      }),
      other: this.other,
      propertyTypes: this.propertyTypes,
      cities: this.cities,
      budget: this.budget
    });
  }

  onSubmit () {
    if (!this.alertForm.valid) return false;
    this.formLoading = true;

    this.submitExecutionSubscription = this.recaptchaV3Service.execute('submit').subscribe(token => {
      if (this.cities.length >= 2) this.cities.shift();
      if (this.propertyTypes.length >= 2) this.propertyTypes.shift();

      this.alertForm.value.cities = this.cities;
      this.alertForm.value.propertyTypes = this.propertyTypes;
      this.alertForm.value.token_captcha = token;

      this.alertService.postAlert(this.alertForm.value).subscribe(response => {
        // console.log(response);

        this.formSend = true;
        this.formLoading = false;

        setTimeout(() => this.smoothScrollService.scrollTo('#alertForm'), 100);

        if (response.success) this.successMessage = true;
        else this.errorMessage = true;
      });
    },
    error => console.error(error));
  }

  onChangeCities ({ target }) {
    if (target.checked) this.cities.push(target.value);
    else this.cities.splice(this.cities.indexOf(target.value), 1);
  }

  onChangePropertyTypes ({ target }) {
    if (target.checked) this.propertyTypes.push(target.value);
    else this.propertyTypes.splice(this.propertyTypes.indexOf(target.value), 1);
  }

  onChangeRange (event) {
    this.budget = event.name;
  }

  resetForm () {
    this.formSend = false;
    this.alertForm.reset();
    this.currentStep = 1;
  }

  retry () {
    this.formSend = false;
    this.currentStep = 1;
  }
}
