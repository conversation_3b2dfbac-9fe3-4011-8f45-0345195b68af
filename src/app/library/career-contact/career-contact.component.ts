import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';

import { Subscription } from 'rxjs';
import { FileUploader } from 'ng2-file-upload';
import { ReCaptchaV3Service } from 'ng-recaptcha';
import { DpDatePickerModule } from 'ng2-date-picker';

import { BlocksService } from '@/services/v3/contentblocks/contentblocks.service';
import { CareerService } from '@/services/v3/career/career.service';
import { GsapScrollSmootherService } from '@/services/v3/utils/gsap-scroll-smoother.service';

const URL = 'path_to_api';

@Component({
  selector: 'lib-career-contact',
  templateUrl: './career-contact.component.html'
})

export class CareerContactComponent implements OnInit {
  public currentStep: number = 1;
  public uploader: FileUploader = new FileUploader({ url: URL });

  roles: any[];

  careerForm: UntypedFormGroup;
  firstName: UntypedFormControl;
  lastName: UntypedFormControl;
  phone: UntypedFormControl;
  phoneExt: UntypedFormControl;
  email: UntypedFormControl;
  address: UntypedFormControl;
  city: UntypedFormControl;
  mydate: UntypedFormControl;
  role: UntypedFormControl;
  message: UntypedFormControl;
  fileInput: UntypedFormControl;
  subject: any;
  successMessage: boolean = false;
  errorMessage: boolean = false;
  formSend: boolean = false;
  formLoading: boolean = false;

  isTyping = false;
  file: any = {};

  selectedDate = new Date();
  datePickerConfig = {
    min: new Date().toLocaleDateString(this.translateService.currentLang === 'fr' ? 'fr-FR' : 'en-US', {
      day: '2-digit',
      month: '2-digit', 
      year: 'numeric'
    }).split('/').join('/'),
    format: this.translateService.currentLang === 'fr' ? 'DD/MM/YYYY' : 'MM/DD/YYYY',
    firstDayOfWeek: this.translateService.currentLang === 'fr' ? 'mo' : 'su'
  };

  public blockTitle: string;
  public blockContent: string;

  // Recaptcha v3 Subscription
  private submitExecutionSubscription: Subscription

  constructor (
    private recaptchaV3Service: ReCaptchaV3Service,
    private blocksService: BlocksService,
    private formBuilder: UntypedFormBuilder,
    private careerService: CareerService,
    public translateService: TranslateService,
    private smoothScrollService: GsapScrollSmootherService
  ) {
    this.translateService.get('library.career-contact.step1.roles').subscribe(res => { this.roles = res; });

    this.blocksService.getBlock('bloc-carriere').subscribe(data => {
      this.blockTitle = data.title;
      this.blockContent = data.text;
    });
  }

  ngOnInit () {
    this.createFormControls();
    this.createForm();
    this.delayRCDisplay();
  }

  delayRCDisplay () {
    setTimeout(() => {
      const result = document.getElementsByClassName('grecaptcha-badge');
      const recaptcha = result.item(0) as HTMLElement;
      recaptcha.style.visibility = 'visible';
    }, 2000);
  }

  ngOnDestroy () {
    const result = document.getElementsByClassName('grecaptcha-badge');
    const recaptcha = result.item(0) as HTMLElement;
    recaptcha.style.visibility = 'hidden';
    if (this.submitExecutionSubscription) { this.submitExecutionSubscription.unsubscribe(); }
  }

  onPreviousStep ($event) {
    $event.preventDefault();
    this.currentStep--;
    setTimeout(() => this.smoothScrollService.scrollTo('#careerForm'), 100);
  }

  onNextStep ($event) {
    $event.preventDefault();
    this.currentStep++;
    setTimeout(() => this.smoothScrollService.scrollTo('#careerForm'), 100);
  }

  private createFormControls () {
    this.subject = new UntypedFormControl('');
    this.firstName = new UntypedFormControl('', Validators.required);
    this.lastName = new UntypedFormControl('', Validators.required);
    this.phone = new UntypedFormControl('', [
      Validators.required,
      Validators.pattern('[0-9]+'),
      Validators.minLength(10)
    ]);
    this.email = new UntypedFormControl('', [
      Validators.required,
      Validators.pattern('^\\w+([\\.-]?\\w+)*@\\w+([\\.-]?\\w+)*(\\.\\w{2,3})+$')
    ]);
    this.phoneExt = new UntypedFormControl('');
    this.address = new UntypedFormControl('');
    this.city = new UntypedFormControl('');
    this.mydate = new UntypedFormControl('', Validators.required);
    this.role = new UntypedFormControl(undefined, Validators.required);
    this.message = new UntypedFormControl('', Validators.required);
    this.fileInput = new UntypedFormControl('', Validators.required);
  }

  private createForm () {
    this.translateService.get('library.career-contact.subject').subscribe(res => { this.subject = res; });
    this.careerForm = this.formBuilder.group({
      firstName: this.firstName,
      lastName: this.lastName,
      phone: this.phone,
      phoneExt: this.phoneExt,
      email: this.email,
      mydate: this.mydate,
      address: this.address,
      city: this.city,
      role: this.role,
      message: this.message,
      file: this.file,
      subject: this.subject,
      fileInput: this.fileInput
    });
  }

  onSubmit () {
    if (!this.careerForm.valid) return false;
    this.formLoading = true;

    this.submitExecutionSubscription = this.recaptchaV3Service.execute('submit').subscribe(token => {
      this.careerForm.value.subject = this.subject;
      this.careerForm.value.file = this.file;
      this.careerForm.value.token_captcha = token;

      this.careerService.postCareer(this.careerForm.value).subscribe(response => {
        this.formSend = true;
        this.formLoading = false;

        if (response.success === true) this.successMessage = true;
        else this.errorMessage = true;
      });
    },
    error => console.error(error));
  }

  onFileChange (event) {
    const reader = new FileReader();

    this.careerForm.controls.fileInput.setErrors(null);
    if (event.target.files && event.target.files.length > 0) {
      const upload = event.target.files[0];
      reader.readAsDataURL(upload);
      reader.onload = () => {
        document.getElementById('label-file-upload').innerText = upload.name;
        this.file.filename = upload.name;
        this.file.filetype = upload.type;
        this.file.file = reader.result;
      };
    }
  }

  resetForm () {
    this.formSend = false;
    this.careerForm.reset();
    this.translateService.get('library.career-contact.step1.upload').subscribe(res => {
      document.getElementById('label-file-upload').innerText = res;
    });

    this.file = '';
    this.currentStep = 1;
  }

  retry () {
    this.formSend = false;
  }
}
