import { Component, OnInit, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ActivatedRoute } from '@angular/router';

import { PropertiesFeaturedComponent } from '@/library/properties-featured/properties-featured.component';
import { InscriptionsService } from '@/services/v3/inscriptions/inscriptions.service';
import { MetatagsService } from '@/services/v3/metatags/metatags.service';
import { OpenhouseService } from '@/services/v3/openhouse/openhouse.service';
import { GsapScrollSmootherService } from '@/services/v3/utils/gsap-scroll-smoother.service';

@Component({
  selector: 'view-buy',
  templateUrl: './buy.component.html'
})

export class BuyComponent implements OnInit {
  @ViewChild(PropertiesFeaturedComponent)
  private propertiesFeaturedComponent: PropertiesFeaturedComponent;

  properties: any;
  propertiesOpenHouse: any;
  pdfGuide: any;
  category: any;

  private fragment: string;

  constructor (
    private openHouseService: OpenhouseService,
    private inscriptionsService: InscriptionsService,
    private translate: TranslateService,
    private metatagsService: MetatagsService,
    private route: ActivatedRoute,
    private smoothScrollService: GsapScrollSmootherService
  ) {
    this.metatagsService.updateMetatags('buy');
  }

  async ngOnInit () {
    this.getProperties();
    this.getOpenHousesProperties();

    this.route.fragment.subscribe(fragment => { this.fragment = fragment; });
  }

  ngAfterViewInit () {
    const f = document.getElementById(this.fragment);
    if (f) this.smoothScrollService.scrollTo(f);
  }

  getProperties () {
    this.inscriptionsService.getInscriptions(6, { featured: 1, sort: 'rand' }).subscribe(({ data }) => {
      if (!data) return;

      this.properties = data;

      if (this.propertiesFeaturedComponent) {
        this.propertiesFeaturedComponent.properties = data;
        this.propertiesFeaturedComponent.initSlider();
      }
    });
  }

  getOpenHousesProperties () {
    this.openHouseService.getOpenhouse('mls').subscribe(data => {
      this.propertiesOpenHouse = data;
    });
  }
}
