import { Component, OnInit } from '@angular/core';
import { TestimonialsService } from '@/services/v3/testimonials/testimonials.service';
import { MetatagsService } from '@/services/v3/metatags/metatags.service';
import { GsapScrollSmootherService } from '@/services/v3/utils/gsap-scroll-smoother.service';

@Component({
  selector: 'view-testimonials',
  templateUrl: './testimonials.component.html'
})

export class TestimonialsComponent implements OnInit {
  public cPage: number = 1;
  public testimonials: any = [];

  constructor (
    private testimonialsService: TestimonialsService,
    private metatagsService: MetatagsService,
    private smoothScrollService: GsapScrollSmootherService
  ) {
    this.testimonialsService.getTestimonials(-1, 'desc').subscribe(({ data }) => {
      this.testimonials = data;
    });

    this.metatagsService.updateMetatags('feedbacks');
  }

  ngOnInit () {
  }

  onPageChange (number: number) {
    this.cPage = number;
    this.smoothScrollService.scrollTo('#testimonial-list');
  }
}
