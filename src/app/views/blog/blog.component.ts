import { Component, OnInit } from '@angular/core';
import { BlogService } from '@/services/v3/blog/blog.service';
import { MetatagsService } from '@/services/v3/metatags/metatags.service';
import { GsapScrollSmootherService } from '@/services/v3/utils/gsap-scroll-smoother.service';

@Component({
  selector: 'view-blog',
  templateUrl: './blog.component.html'
})

export class BlogComponent implements OnInit {
  public blogs = [];
  public blogCategories = [];

  public cPage: number = 1;
  public currentCategory: string;
  public totalLength: number;
  public limitPerPage = 4;

  constructor (
    private blogService: BlogService,
    private metatagsService: MetatagsService,
    private smoothScrollService: GsapScrollSmootherService
  ) {
    this.metatagsService.updateMetatags('blog');
  }

  ngOnInit () {
    this.blogService.getPosts('', '').subscribe(({ data }) => {
      this.blogs = data;
      this.totalLength = data.length;
    });

    // Get blog categories
    this.blogService.getCategories().subscribe(({ data }) => {
      this.blogCategories = data;
    });
  }

  onPageChange (number: number) {
    this.cPage = number;
    this.smoothScrollService.scrollTo('#blog-list');
  }

  onCategoryChange (categorySlug: string) {
    this.currentCategory = categorySlug;
    this.cPage = 1;
    this.totalLength = this.blogs.filter(p => p.category_slug === categorySlug).length;
  }
}
