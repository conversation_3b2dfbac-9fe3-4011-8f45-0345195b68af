.search-properties-pane-cpn{
	position: relative;
	margin: 0;
	z-index: 10;
	width: 50%;
	background-color: var(--color-bg);
	min-height: 500px;
	box-shadow: 2px 0 4px 0 rgba(0,0,0,0.10);
	transition: $transition;
	margin-left: -50%;

  .properties-menu {

    .ng-select {
      
      .ng-option-marked, .ng-option-selected { background-color: #F1ECE1 !important; }

      &.transparent {
        *:not(.ng-arrow) {
          background-color: #F1ECE1;
          border: 0px none;
        }

        .ng-select-container, .ng-dropdown-panel {
          border: 0px none;
          box-shadow: none;
		  min-width:120px;
        }
      }
    }
  }

	.properties-list-cpn { 
		padding-top: 0; 
		padding-bottom: 1px;
	}

	.loading {
		padding-top: 150px;
		opacity: 1 !important;
	}

	.properties {
		width: calc(100% * 1/2 - 10px);
		&:nth-child(3n) { margin-right: 20px; }
		&:nth-child(2n) { margin-right: 0; }
	}

	.toggle-pane {
		position: sticky;
		top: 185px;
		right: 0px;
		margin-right: -30px;
		margin-top: 20px;
		float: right;
		display: block;
		width: 30px;
		line-height: 60px;
		text-align: center;
		background-color: var(--primary-color);
		color: white;
		transition: $transition;
		cursor:pointer;
		opacity: 1;
		transition: $transition;

		div { transform: rotate(180deg); }
		&:hover { background-color: rgba(var(--primary-color), .60); }

		&.selected {
			pointer-events: none; 
			background-color: rgba(153,153,153,0.7);
		}

		&.toggle-pane-1 {
			margin-bottom:65px;
		}

		&.toggle-pane-2 {
		top: 250px;
		margin-top: 85px;
		}
	}

	&.open {
		margin: 0;

		.toggle-pane-1 {
			pointer-events: auto; 
			background-color: var(--primary-color);
			&:hover { background-color: rgba(var(--primary-color), .60); }
		}
	}

	&.-grid {
		width: calc(100% - 80px) !important;
		margin: 0;

		.properties-list-cpn { margin: 0 auto; }
		
		.properties {
			width: calc(100% * 1/3 - 14px);

			&:nth-child(2n) {
				margin-right: 20px;
			}

			&:nth-child(3n) {
				margin-right: 0px;
			}

			.properties-info .more-info .align {
				@media (max-width: 1200px) {
					display: flex;
				}
			}
		}

		.toggle-pane {
			right: 0px;
    		margin-right: -46px;
		}
	}

	.no-results{
		color: var(--color-text);
		padding: rs(60px, 120px) rem(32px) 0;

		.txt1{
			margin-top: 60px;
		}

		.txt2{
			font-size: var(--font-size-cta-small);
			margin-top: 20px;
		}

		a { margin: 40px auto; }
	}

  @media (min-width: 993px)and (max-width: 1200px) {
    .properties .properties-info .more-info .align { display: none; }
  }

  @media (max-width: 992px) {
    display: none;
		
		&.show {
			display: block;
			width: 100% !important;
			margin: 0;
		}

    .loading {
      margin-top: 100px;
    }

    .toggle-pane {
			display: none;
    }
  }

	@media (max-width: 767px) {
		&.show {
			.properties {
				width: 100%;
				margin-right: 0;
			}
		}
	}
}
